<template>
  <div class="mainBody">
    <a-row>
      <a-col :span="8" class="showClass">
        <div class="searchClass">
          <a-input-search
            v-model:value="searchVal"
            placeholder="文件夹名"
            enter-button
            @search="onSearch"
          />
          <a-button type="primary" class="addClass" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增文件夹</a-button>

          <a-alert message="右键点击文件夹，可进行编辑、添加下级、删除" type="info" show-icon />
          <a-tree
            :show-line="true"
            :tree-data="treeData"
            v-model:selectedKeys="selectedKeys"
            @select="handleTreeSelect"
          >

            <template #title="{ key: treeKey, title, sort }">
              <a-dropdown :trigger="['contextmenu']">
                <span>{{ title }}</span>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1">
                      <a-button type="text" @click="onContextMenuClick(treeKey, title, '1', sort)">编辑</a-button>
                    </a-menu-item>
                    <a-menu-item key="2">
                      <a-button type="text" @click="onContextMenuClick(treeKey, title, '2')">添加下级</a-button>
                    </a-menu-item>
                    <a-menu-item key="3">
                      <a-popconfirm
                        title="确认删除?"
                        @confirm="onContextMenuClick(treeKey, title, '3')"
                        @cancel="cancel"
                      >
                        <a-button type="text">删除</a-button>
                      </a-popconfirm>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </a-tree>
        </div>
      </a-col>
      <a-col :span="16" class="showClass rightCol">
        <!--引用表格-->
        <BasicTable @register="registerTable">
        <!--插槽:table标题-->
          <template #tableTitle>
              <a-button type="primary" @click="handleFileAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
              <!-- <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
              <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
              <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown> -->
          </template>
          <template #showPdf="{ record }">
            <div>
              <!-- <div v-if="record.file != null && record.file.length > 0">
                <div v-for="item,index in record.file.split(',')" :key="index" class="pdfFileUrl">
                  <a @click="pdfPreview(item)">{{index+1}}.{{ item.substring(8) }}</a>
                </div>
              </div> -->
              <a @click="openFile(record.file)">{{ record.file }}</a>

            </div>
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)"/>
          </template>
        </BasicTable>
      </a-col>
    </a-row>
    

    <!-- 表单区域 -->
    <PromotionModal @register="registerModal" @success="handleIns"></PromotionModal>
    <PromotionFileModal @register="regFileModal" @success="handleSuccess"></PromotionFileModal>
    <div style="display: none">
      <iframe id="pdfPreviewIframe" :src="url" frameborder="0" width="100%" height="550px" scrolling="auto"></iframe>
    </div>
  </div>
</template>

<script lang="ts" name="limsParameterSet-limsParameterSet" setup>
  import {ref, computed, unref, watch, onMounted} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import PromotionModal from './modules/PromotionModal.vue'
  import PromotionFileModal from './modules/PromotionFileModal.vue'
  import {columns, searchFormSchema} from './Promotion.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl,deleteFile,treeList} from './Promotion.api';
  import { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
  import { message } from 'ant-design-vue';
  import { getToken } from '/@/utils/auth';
  import { useGlobSetting } from '/@/hooks/setting';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { encryptByBase64 } from "@/utils/cipher";

  const glob = useGlobSetting();
  const url = ref(`${glob.domainUrl}/sys/common/pdf/pdfPreviewIframe`);
  const searchVal = ref<string>('')
  const curKey = ref<string>('')
  const treeData: TreeDataItem[] = ref([
    {
      title: 'parent 1',
      key: '0-0',
      children: [
        {
          title: 'parent 1-0',
          key: '0-0-0',
          children: [
            { title: 'leaf', key: '0-0-0-0' },
            { title: 'leaf', key: '0-0-0-1' },
          ],
        },
        {
          title: 'parent 1-1',
          key: '0-0-1',
          children: [{ key: '0-0-1-0', title: 'leaf'}],
        },
      ],
    },
  ]);
  // const expandedKeys = ref<string[]>(['0-0-0', '0-0-1']);
  const selectedKeys = ref<string[]>([]);
  // const checkedKeys = ref<string[]>(['0-0-0', '0-0-1']);
  // watch(expandedKeys, () => {
  //   console.log('expandedKeys', expandedKeys);
  // });
  onMounted(async () => {
    await onSearch()
  })
  watch(selectedKeys, () => {
    console.log('selectedKeys', selectedKeys);
  });
  // watch(checkedKeys, () => {
  //   console.log('checkedKeys', checkedKeys);
  // });

  //注册model
  const [registerModal, {openModal}] = useModal();
  const [regFileModal, {openModal: openFileModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
    tableProps:{
      title: '市场洞察报告',
      api: list,
      columns,
      canResize:false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter:true,
        showAdvancedButton:true,
        showResetButton: false,
        fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
      },
      showTableSetting: false,
      immediate: false,
      actionColumn: {
        width: 120,
        fixed: 'left',
      },
    },
    exportConfig: {
      name:"lims参数设置",
      url: getExportUrl,
    },
    importConfig: {
      url: getImportUrl
    },
  })

  const [registerTable, {reload, getForm},{ rowSelection, selectedRowKeys }] = tableContext

  const onSearch = (searchValue: string) => {
    // if(searchValue != null && searchValue != undefined && searchValue != '') {

    // } else {
    //   message.warning('分类名称必填')
    // }
    console.log('use value', searchValue);
    treeData.value = []
    treeList({name: searchValue}).then((res) => {
      console.log("🚀 ~ treeList ~ res:", res)
      for(let i = 0; i < res.length; i++) {
        treeData.value.push(res[i])
      }
    })
  };
  /**
    * 左侧分类新增
    */
  function handleAdd() {
    openModal(true, {
      option: 'add',
      isUpdate: false,
      showFooter: true,
    });
  }
  // 左侧 添加下级、编辑、删除
  const onContextMenuClick = async (treeKey: string, showTitle: string, menuKey: string, sort: number) => {
    console.log(`treeKey: ${treeKey}, showTitle: ${showTitle}, menuKey: ${menuKey}`);
    if(menuKey == '1') {
      openModal(true, {
        record: { id: treeKey, name: showTitle, sort: sort },
        option: 'edit',
        isUpdate: true,
        showFooter: true,
      });
    } else if(menuKey == '2') {
      openModal(true, {
        record: { parentId: treeKey },
        option: 'add',
        isUpdate: true,
        showFooter: true,
      });
    } else if(menuKey == '3') {
      await deleteOne({id: treeKey}, onSearch);
    }
  };
  function handleTreeSelect(selectedKeys, e:{selected: bool, selectedNodes, node, event}) {
    console.log("🚀 ~ handleTreeSelect ~ selectedKeys:", selectedKeys)
    // console.log("🚀 ~ handleTreeSelect ~ e:", e)
    curKey.value = selectedKeys[0]
    console.log("🚀 ~ handleTreeSelect ~ curKey.value:", curKey.value)
    getForm().setFieldsValue({
      parentId: curKey.value
    })
    if(curKey.value != null && curKey.value != undefined && curKey.value != '') {
      reload()
    }
    
  }
  /** 右侧文件新增 */
  function handleFileAdd() {
    if(curKey.value != null && curKey.value.length > 0) {
      openFileModal(true, {
        record: { parentId: curKey.value },
        isUpdate: false,
        showFooter: true,
      });
    } else {
      message.warning('请先选中文件夹！')
    }
    
  }
  /** 右侧文件编辑 */
  function handleFileEdit(record: Recordable) {
    if(curKey.value != null && curKey.value.length > 0) {
      openFileModal(true, {
        record,
        isUpdate: true,
        showFooter: true,
      });
    } else {
      message.warning('请先选中文件夹！')
    }
    
  }
  /**
    * 详情
    */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
    * 删除事件
    */
  async function handleDelete(record) {
    await deleteFile({id: record.id}, reload);
  }
  /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
    await batchDelete({ids: selectedRowKeys.value}, reload);
  }

  function pdfPreview(title) {
    let iframe = document.getElementById('pdfPreviewIframe');
    let json = { title: title, token: getToken() };
    iframe.contentWindow.postMessage(json, '*');
  }
  function openFile(title) {
    defHttp1.get({ url: '/pm/clockIn/previewPic?objectKey=' + title }).then((res) => {
      // let url = encodeURIComponent(encryptByBase64(getFullImageUrl(res)));
      // previewUrl.value = 'http://fileview.colori.com:8022/onlinePreview?url=' + url;
      previewUrl.value = getFullImageUrl(res.result);
      previewUrlList.value = res.result.split(',').map(item => {
        return getFullImageUrl(item);
      });
      previewModalVisible.value = true;
    });
  }
  /**
  * 成功回调
  */
  function handleIns({isUpdate, values}) {
    onSearch()
  }
  function handleSuccess({isUpdate, values}) {
    reload();
  }
  /**
    * 操作栏
    */
  function getTableAction(record){
    return [
      {
        label: '编辑',
        onClick: handleFileEdit.bind(null, record),
      }, 
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        }
      }
    ]
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record){
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }
    ]
  }
</script>
<style scoped>
.mainBody {
  width: 98%;
  margin: 10px auto;
  /* background-color: rgb(60, 255, 0); */
}
.container {
  /* background-color: red; */
  /* margin: 8px auto;
  width: 100%; */
}
.showClass {
  background-color: white;
}
.searchClass {
  margin: 0px auto;
  margin-top: 21px;
  width: 90%;
  
}
.rightCol {
  border-left: 8px solid #F0F2F5;
}
.addClass {
  margin-top: 8px;
  margin-bottom: 8px;
}
.pdfFileUrl {
  text-align: left;
}
</style>