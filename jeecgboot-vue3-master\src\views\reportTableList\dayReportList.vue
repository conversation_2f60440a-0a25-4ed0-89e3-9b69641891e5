<template>
  <div>
    <!--引用表格-->
    <!-- :rowSelection="rowSelection" -->
    <BasicTable @register="registerTable">
      <!-- <template #form-conditionSelect="{ model, field }">
                <a-select placeholder="请选择" v-model:value="model[field]" allowClear @change="conditionSelectChang">
                    <a-select-option :value="'年'">年</a-select-option>
                    <a-select-option :value="'季'">季</a-select-option>
                    <a-select-option :value="'月'">月</a-select-option>
                </a-select>
            </template> -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
        <!-- <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload">查询</a-button> -->
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportReportXls"> 导出</a-button>
        <!-- <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>  -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
            <template #overlay>
              <a-menu>
                <a-menu-item key="1" @click="batchHandleDelete">
                  <Icon icon="ant-design:delete-outlined"></Icon>
                  删除
                </a-menu-item>
              </a-menu>
            </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <template #action="{ record }">
        <!-- :dropDownActions="getDropDownAction(record)" -->
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #file="{ record }">
        <a @click="handlePreview(record)">{{ record.file }}</a>
      </template>
      <!--操作栏-->
      <template #username="{ record, text }">
        <a @click="openDetail(record)">{{ text }}</a>
      </template>
      <!--字段回显插槽-->
      <!-- <template #htmlSlot="{text}">
           <div v-html="text"></div>
        </template> -->
    </BasicTable>

    <!-- 详情模态框 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="工作日报详情"
      :width="800"
      @ok="handleDetailModalOk"
      @cancel="handleDetailModalCancel"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="用户编码">
          {{ detailFormData.user_code }}
        </a-descriptions-item>
        <a-descriptions-item label="用户名称">
          {{ detailFormData.user_name }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ detailFormData.create_time }}
        </a-descriptions-item>
        <a-descriptions-item label="今日工作内容">
          <div style="white-space: pre-wrap; word-break: break-all;">{{ detailFormData.today_work_summary }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="明日工作计划">
          <div style="white-space: pre-wrap; word-break: break-all;">{{ detailFormData.tomorrow_work_plan }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="需协调事项">
          <div style="white-space: pre-wrap; word-break: break-all;">{{ detailFormData.coordinate_matter }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

  </div>
</template>
<script lang="ts" name="reportTableList-dayReportList" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, searchFormSchema } from './dayReportList.data';
import { list } from './dayReportList.api';
import { defHttp } from '/@/utils/http/axios';
import { encryptByBase64 } from "@/utils/cipher";

// 详情模态框状态
const detailModalVisible = ref(false);
const detailFormData = ref({
  user_code: '',
  user_name: '',
  today_work_summary: '',
  tomorrow_work_plan: '',
  coordinate_matter: '',
  create_time: '',
});
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '工作日报',
    api: list,
    columns: columns,
    canResize: false,
    immediate: true,
    ellipsis: false,
    useSearchForm: true,
    defSort: {
      column: 'create_time',
      order: 'desc',
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [['data', ['begTime', 'endTime'], 'YYYY-MM']],
    },
    pagination: {
      // isFetch: false
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'left',
    },
    beforeFetch: (params) => {
      delete params.order
      delete params.column
      return params;
    },
  },
  exportConfig: {
    name: '工作日报',
    url: '/online/cgreport/api/exportManySheetXls/1945003985678835713',
  },
  //     importConfig: {
  //       url: getImportUrl,
  //       success: handleSuccess
  //     },
});



const [registerTable, { reload, getForm, setColumns }, { rowSelection, selectedRowKeys }] = tableContext;

async function onExportReportXls() {
  const data = await defHttp.get({ url: '/online/cgreport/api/exportManySheetXls/1945003985678835713', params: getForm().getFieldsValue(), responseType: 'blob', timeout: 600000 }, { isTransformResponse: false });
  let blobOptions = { type: 'application/vnd.ms-excel' };
  let fileSuffix = '.xls';
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(new Blob([data], blobOptions), '工作日报' + fileSuffix);
  } else {
    let url = window.URL.createObjectURL(new Blob([data], blobOptions));
    let link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', '工作日报' + fileSuffix);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
function handlePreview(record) {
  let url = encodeURIComponent(encryptByBase64(import.meta.env.VITE_GLOB_DOMAIN_URL + '/sys/common/static/' + record.file));
  window.open('http://fileview.colori.com:8022/onlinePreview?url=' + url);
}

function handleDetail(record) {
  // 设置表单数据
  detailFormData.value = {
    user_code: record.user_code || '',
    user_name: record.user_name || '',
    today_work_summary: record.today_work_summary || '',
    tomorrow_work_plan: record.tomorrow_work_plan || '',
    coordinate_matter: record.coordinate_matter || '',
    create_time: record.create_time || '',
  };
  // 显示模态框
  detailModalVisible.value = true;
}

// 模态框确认按钮处理
function handleDetailModalOk() {
  detailModalVisible.value = false;
}

// 模态框取消按钮处理
function handleDetailModalCancel() {
  detailModalVisible.value = false;
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    // {
    //   label: '修改保质期',
    //   onClick: editqualityDay.bind(null, record),
    // },
  ];
}
</script>

<style scoped></style>