import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { defHttp } from '/@/utils/http/axios';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '用户编码',
    resizable: true,
    align: 'center',
    dataIndex: 'user_code',
    width: 120,
  },
  {
    title: '用户名称',
    resizable: true,
    align: 'center',
    dataIndex: 'user_name',
    width: 120,
  },
  {
    title: '今日工作内容',
    resizable: true,
    align: 'center',
    dataIndex: 'today_work_summary',
    width: 200,
    ellipsis: true,
  },
  {
    title: '明日工作计划',
    resizable: true,
    align: 'center',
    dataIndex: 'tomorrow_work_plan',
    width: 200,
    ellipsis: true,
  },
  {
    title: '需协调事项',
    resizable: true,
    align: 'center',
    dataIndex: 'coordinate_matter',
    width: 200,
    ellipsis: true,
  },
  {
    title: '创建时间',
    resizable: true,
    align: 'center',
    dataIndex: 'create_time',
    width: 160,
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '用户编码',
    field: 'user_code',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '用户名称',
    field: 'user_name',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '开始日期',
    field: 'day_begin',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '结束日期',
    field: 'day_end',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];