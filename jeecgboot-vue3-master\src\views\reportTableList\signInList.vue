<template>
  <div>
    <!--引用表格-->
    <!-- :rowSelection="rowSelection" -->
    <BasicTable @register="registerTable">
      <!-- <template #form-conditionSelect="{ model, field }">
                <a-select placeholder="请选择" v-model:value="model[field]" allowClear @change="conditionSelectChang">
                    <a-select-option :value="'年'">年</a-select-option>
                    <a-select-option :value="'季'">季</a-select-option>
                    <a-select-option :value="'月'">月</a-select-option>
                </a-select>
            </template> -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
        <!-- <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload">查询</a-button> -->
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportReportXls"> 导出</a-button>
        <!-- <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>  -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
            <template #overlay>
              <a-menu>
                <a-menu-item key="1" @click="batchHandleDelete">
                  <Icon icon="ant-design:delete-outlined"></Icon>
                  删除
                </a-menu-item>
              </a-menu>
            </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <template #action="{ record }">
        <!-- :dropDownActions="getDropDownAction(record)" -->
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #pic="{ record }">
        <a @click="handlePreview(record)">查看</a>
      </template>

      <!--字段回显插槽-->
      <!-- <template #htmlSlot="{text}">
           <div v-html="text"></div>
        </template> -->
    </BasicTable>

    <!-- 详情模态框 -->
    <a-modal v-model:visible="detailModalVisible" title="打卡记录详情" :width="700" @ok="handleDetailModalOk"
      @cancel="handleDetailModalCancel">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="用户编码">
          {{ detailFormData.user_code }}
        </a-descriptions-item>
        <a-descriptions-item label="用户名称">
          {{ detailFormData.user_name }}
        </a-descriptions-item>
        <a-descriptions-item label="签到/签退时间" :span="2">
          {{ detailFormData.check_in_time }}
        </a-descriptions-item>
        <a-descriptions-item label="省">
          {{ detailFormData.province }}
        </a-descriptions-item>
        <a-descriptions-item label="市">
          {{ detailFormData.city }}
        </a-descriptions-item>
        <a-descriptions-item label="区">
          {{ detailFormData.district }}
        </a-descriptions-item>
        <a-descriptions-item label="类型">
          {{ detailFormData.typeText }}
        </a-descriptions-item>
        <a-descriptions-item label="地址" :span="2">
          {{ detailFormData.address }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          {{ detailFormData.status }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 图片预览模态框 -->
    <a-modal v-model:visible="previewModalVisible" title="图片预览" :width="500" :footer="null"
      @cancel="handlePreviewModalCancel">
      <div style="height: 400px;padding-top: 100px;">
        <!-- <iframe :src="previewUrl" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe> -->
        <TableImg :size="150"  :imgList="previewUrlList" />

      </div>
    </a-modal>

  </div>
</template>
<script lang="ts" name="reportTableList-signInList" setup>
import { ref } from 'vue';
import { BasicTable, TableAction, TableImg } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, searchFormSchema } from './signInList.data';
import { list } from './signInList.api';
import { defHttp } from '/@/utils/http/axios';
import { defHttp1 } from '/@/utils/http/axios/index1';
import { encryptByBase64 } from "@/utils/cipher";

// 详情模态框状态
const detailModalVisible = ref(false);
const detailFormData = ref({
  user_code: '',
  user_name: '',
  check_in_time: '',
  province: '',
  city: '',
  district: '',
  address: '',
  type: '',
  typeText: '',
  status: '',
});

// 预览模态框状态
const previewModalVisible = ref(false);
const previewUrl = ref('');
const previewUrlList = ref([]);
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '打卡记录',
    api: list,
    columns: columns,
    canResize: false,
    immediate: true,
    ellipsis: false,
    useSearchForm: true,
    defSort: {
      column: 'create_time',
      order: 'desc',
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [['data', ['begTime', 'endTime'], 'YYYY-MM']],
    },
    pagination: {
      // isFetch: false
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'left',
    },
    beforeFetch: (params) => {
      delete params.order
      delete params.column
      return params;
    },
  },
  exportConfig: {
    name: '打卡记录',
    url: '/online/cgreport/api/exportManySheetXls/1937697750037876738',
  },
  //     importConfig: {
  //       url: getImportUrl,
  //       success: handleSuccess
  //     },
});



const [registerTable, { reload, getForm, setColumns }, { rowSelection, selectedRowKeys }] = tableContext;

async function onExportReportXls() {
  const data = await defHttp.get({ url: '/online/cgreport/api/exportManySheetXls/1937697750037876738', params: getForm().getFieldsValue(), responseType: 'blob', timeout: 600000 }, { isTransformResponse: false });
  let blobOptions = { type: 'application/vnd.ms-excel' };
  let fileSuffix = '.xls';
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(new Blob([data], blobOptions), '打卡记录' + fileSuffix);
  } else {
    let url = window.URL.createObjectURL(new Blob([data], blobOptions));
    let link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', '打卡记录' + fileSuffix);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
function getFullImageUrl(url) {
  if (!url) return '';
  if (url.startsWith('http')) {
    return url;
  }
  const domain = import.meta.env.VITE_GLOB_DOMAIN_URL + '/sys/common/static/' || '';
  // 移除 domain 尾部的 /
  const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
  // 移除 url 头部的 /
  const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

  if (!cleanDomain) return `/${cleanUrl}`;

  return `${cleanDomain}/${cleanUrl}`;
}
function handlePreview(record) {
  defHttp1.get({ url: '/pm/clockIn/previewPic?objectKey=' + record.pic }).then((res) => {
    // let url = encodeURIComponent(encryptByBase64(getFullImageUrl(res)));
    previewUrl.value = getFullImageUrl(res.result);
    previewUrlList.value = res.result.split(',').map(item=>{
      return getFullImageUrl(item);
    });
    previewModalVisible.value = true;
  });
}
function handleDetail(record) {
  // 设置表单数据
  detailFormData.value = {
    user_code: record.user_code || '',
    user_name: record.user_name || '',
    check_in_time: record.check_in_time || '',
    province: record.province || '',
    city: record.city || '',
    district: record.district || '',
    address: record.address || '',
    type: record.type || '',
    typeText: record.type == 1 ? '签到' : record.type == 2 ? '签退' : '',
    status: record.status || '',
  };
  // 显示模态框
  detailModalVisible.value = true;
}

// 模态框确认按钮处理
function handleDetailModalOk() {
  detailModalVisible.value = false;
}

// 模态框取消按钮处理
function handleDetailModalCancel() {
  detailModalVisible.value = false;
}

// 预览模态框取消按钮处理
function handlePreviewModalCancel() {
  previewModalVisible.value = false;
  previewUrl.value = '';
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    // {
    //   label: '修改保质期',
    //   onClick: editqualityDay.bind(null, record),
    // },
  ];
}
</script>

<style scoped></style>