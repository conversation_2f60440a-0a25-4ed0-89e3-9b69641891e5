import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { defHttp } from '/@/utils/http/axios';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '用户编码',
    resizable: true,
    align: 'center',
    dataIndex: 'user_code',
    width: 120,
  },
  {
    title: '用户名称',
    resizable: true,
    align: 'center',
    dataIndex: 'user_name',
    width: 120,
  },
  {
    title: '类型',
    resizable: true,
    align: 'center',
    dataIndex: 'type',
    width: 100,
  },
  {
    title: '编码',
    resizable: true,
    align: 'center',
    dataIndex: 'customer_code',
    width: 120,
    customRender: ({ record, text }) => {
      if (record.type == '客户') {
        return record.customer_code;
      } else {
        return ''
      }
    },
  },
  {
    title: '名称',
    resizable: true,
    align: 'center',
    dataIndex: 'customer_name',
    width: 150,
    customRender: ({ record, text }) => {
      if (record.type == '客户') {
        return record.customer_name;
      } else if (record.type == '意向客户'){
        return record.customer_name;
      } else if (record.type == '体系'){
        return record.system_name;
      } else if (record.type == '分销商'){
        return record.distributor_name;
      }
    },
  },
  // {
  //   title: '体系名称',
  //   resizable: true,
  //   align: 'center',
  //   dataIndex: 'system_name',
  //   width: 150,
  // },
  // {
  //   title: '分销商名称',
  //   resizable: true,
  //   align: 'center',
  //   dataIndex: 'distributor_name',
  //   width: 150,
  // },
  {
    title: '计划拜访日期',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_date',
    width: 120,
  },
  {
    title: '计划拜访时间',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_time',
    width: 120,
  },
  {
    title: '拜访目的',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_purpose',
    width: 200,
    ellipsis: true,
  },
  {
    title: '拜访内容及成果',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_content',
    width: 200,
    ellipsis: true,
  },
  {
    title: '拜访照片',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_photos',
    width: 120,
    slots: { customRender: 'visitPhotos' },
  },
  {
    title: '拜访地址',
    resizable: true,
    align: 'center',
    dataIndex: 'visit_address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '省',
    resizable: true,
    align: 'center',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '市',
    resizable: true,
    align: 'center',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '区',
    resizable: true,
    align: 'center',
    dataIndex: 'district',
    width: 100,
  },
  {
    title: '实际拜访时间',
    resizable: true,
    align: 'center',
    dataIndex: 'actual_start_time',
    width: 160,
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '用户编码',
    field: 'user_code',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '用户名称',
    field: 'user_name',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '开始日期',
    field: 'visit_date_begin',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '结束日期',
    field: 'visit_date_end',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];