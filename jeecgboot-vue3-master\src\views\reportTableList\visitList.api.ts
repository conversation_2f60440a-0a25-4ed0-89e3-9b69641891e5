import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/online/cgreport/api/getData/1945403469537685506',
  custReport = '/pms/tracking/list',
  save = '/wms/code/addWhCode',
  edit = '/wms/code/addWhCodeDetail',
  goodsListsave = '/wms/rack/add',
  goodsListedit = '/wms/rack/edit',
  deleteOne = '/adm/atest/delete',
  goodsListdeleteOne = '/wms/rack/delete',
  deleteBatch = '/adm/atest/deleteBatch',
  importExcel = '/adm/atest/importExcel',
  exportXls = '/online/cgreport/api/exportManySheetXls/1945403469537685506',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * list
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });
export const custReport = (params) => defHttp.get({ url: Api.custReport, params });
/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 删除单个
 */
export const goodsListdeleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.goodsListdeleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
/**
 * 保存或者更新
 * @param params
 */
export const goodsListsaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.goodsListedit : Api.goodsListsave;
  return defHttp.post({ url: url, params });
};
