<template>
  <div>
    <!--引用表格-->
    <!-- :rowSelection="rowSelection" -->
    <BasicTable @register="registerTable">
      <!-- <template #form-conditionSelect="{ model, field }">
                <a-select placeholder="请选择" v-model:value="model[field]" allowClear @change="conditionSelectChang">
                    <a-select-option :value="'年'">年</a-select-option>
                    <a-select-option :value="'季'">季</a-select-option>
                    <a-select-option :value="'月'">月</a-select-option>
                </a-select>
            </template> -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
        <!-- <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload">查询</a-button> -->
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportReportXls"> 导出</a-button>
        <!-- <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>  -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
            <template #overlay>
              <a-menu>
                <a-menu-item key="1" @click="batchHandleDelete">
                  <Icon icon="ant-design:delete-outlined"></Icon>
                  删除
                </a-menu-item>
              </a-menu>
            </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <template #action="{ record }">
        <!-- :dropDownActions="getDropDownAction(record)" -->
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #visitPhotos="{ record }">
        <a v-if='record.visit_photos' @click="handlePreview(record)">查看</a>
      </template>

      <!--字段回显插槽-->
      <!-- <template #htmlSlot="{text}">
           <div v-html="text"></div>
        </template> -->
    </BasicTable>

    <!-- 详情模态框 -->
    <a-modal v-model:visible="detailModalVisible" title="拜访记录详情" :width="900" @ok="handleDetailModalOk"
      @cancel="handleDetailModalCancel">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="用户编码">
          {{ detailFormData.user_name }} - {{ detailFormData.user_code }}
        </a-descriptions-item>
        <a-descriptions-item label="类型">
          {{ detailFormData.type }}
        </a-descriptions-item>
        <a-descriptions-item label="客户编码">
          <template v-if="detailFormData.type == '客户'">
            {{ detailFormData.customer_code }}
          </template>
          <template v-else>
          </template>
        </a-descriptions-item>
        <a-descriptions-item label="客户名称">
          <template v-if="detailFormData.type == '客户'">
            {{ detailFormData.customer_name }}
          </template>
          <template v-else-if="detailFormData.type == '意向客户'">
            {{ detailFormData.customer_name }}
          </template>
          <template v-else-if="detailFormData.type == '体系'">
            {{ detailFormData.system_name }}
          </template>
          <template v-else-if="detailFormData.type == '分销商'">
            {{ detailFormData.distributor_name }}
          </template>
        </a-descriptions-item>
        <a-descriptions-item label="计划拜访日期">
          {{ detailFormData.visit_date }} {{ detailFormData.visit_time }}
        </a-descriptions-item>
        <a-descriptions-item label="拜访目的" :span="2">
          <div style="white-space: pre-wrap; word-break: break-all;">{{ detailFormData.visit_purpose }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="拜访内容及成果" :span="2">
          <div style="white-space: pre-wrap; word-break: break-all;">{{ detailFormData.visit_content }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="省市区" :span="2">
          {{ detailFormData.province }}-{{ detailFormData.city }}-{{ detailFormData.district }}
        </a-descriptions-item>
        <a-descriptions-item label="拜访地址" :span="2">
          {{ detailFormData.visit_address }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
    <!-- 图片预览模态框 -->
    <a-modal v-model:visible="previewModalVisible" title="图片预览" :width="500" :footer="null"
      @cancel="handlePreviewModalCancel">
        <div style="height: 400px;padding-top: 100px;">
        <!-- <iframe :src="previewUrl" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe> -->
        <!-- <TableImg :size="600" :simpleShow="true" :imgList="[previewUrl]" /> -->
        <TableImg :size="150"  :imgList="previewUrlList" />
        <!-- <iframe :src="previewUrl" style="width: 100%; height: 100%; border: none;" frameborder="0"></iframe> -->
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" name="reportTableList-visitList" setup>
import { ref } from 'vue';
import { BasicTable, TableAction, TableImg } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, searchFormSchema } from './visitList.data';
import { list } from './visitList.api';
import { defHttp } from '/@/utils/http/axios';
import { defHttp1 } from '/@/utils/http/axios/index1';
import { encryptByBase64 } from "@/utils/cipher";
// 预览模态框状态
const previewModalVisible = ref(false);
const previewUrl = ref('');
const previewUrlList = ref([]);

// 详情模态框状态
const detailModalVisible = ref(false);
const detailFormData = ref({
  user_code: '',
  user_name: '',
  type: '',
  customer_code: '',
  customer_name: '',
  system_name: '',
  distributor_name: '',
  visit_date: '',
  visit_time: '',
  visit_purpose: '',
  visit_content: '',
  visit_photos: '',
  visit_address: '',
  province: '',
  city: '',
  district: '',
  actual_start_time: '',
});
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '拜访记录',
    api: list,
    columns: columns,
    canResize: false,
    immediate: true,
    ellipsis: false,
    useSearchForm: true,
    defSort: {
      column: 'create_time',
      order: 'desc',
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [['data', ['begTime', 'endTime'], 'YYYY-MM']],
    },
    pagination: {
      // isFetch: false
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'left',
    },
    beforeFetch: (params) => {
      delete params.order
      delete params.column
      return params;
    },
  },
  exportConfig: {
    name: '拜访记录',
    url: '/online/cgreport/api/exportManySheetXls/1945403469537685506',
  },
  //     importConfig: {
  //       url: getImportUrl,
  //       success: handleSuccess
  //     },
});



const [registerTable, { reload, getForm, setColumns }, { rowSelection, selectedRowKeys }] = tableContext;

async function onExportReportXls() {
  const data = await defHttp.get({ url: '/online/cgreport/api/exportManySheetXls/1945403469537685506', params: getForm().getFieldsValue(), responseType: 'blob', timeout: 600000 }, { isTransformResponse: false });
  let blobOptions = { type: 'application/vnd.ms-excel' };
  let fileSuffix = '.xls';
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(new Blob([data], blobOptions), '拜访记录' + fileSuffix);
  } else {
    let url = window.URL.createObjectURL(new Blob([data], blobOptions));
    let link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', '拜访记录' + fileSuffix);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
// function handlePreview(record) {
//   let url = encodeURIComponent(encryptByBase64(import.meta.env.VITE_GLOB_DOMAIN_URL + '/sys/common/static/' + record.file));
//   window.open('http://fileview.colori.com:8022/onlinePreview?url=' + url);
// }
function getFullImageUrl(url) {
  if (!url) return '';
  if (url.startsWith('http')) {
    return url;
  }
  const domain = import.meta.env.VITE_GLOB_DOMAIN_URL + '/sys/common/static/' || '';
  // 移除 domain 尾部的 /
  const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
  // 移除 url 头部的 /
  const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

  if (!cleanDomain) return `/${cleanUrl}`;

  return `${cleanDomain}/${cleanUrl}`;
}
function handlePreview(record) {
  console.log("🚀 ~ handlePreview ~ record:", record)
  defHttp1.get({ url: '/pm/clockIn/previewPic?objectKey=' + record.visit_photos }).then((res) => {
    // let url = encodeURIComponent(encryptByBase64(getFullImageUrl(res)));
    // previewUrl.value = 'http://fileview.colori.com:8022/onlinePreview?url=' + url;
    previewUrl.value = getFullImageUrl(res.result);
    previewUrlList.value = res.result.split(',').map(item => {
      return getFullImageUrl(item);
    });
    previewModalVisible.value = true;
  });
}
function handleDetail(record) {
  // 设置表单数据
  detailFormData.value = {
    user_code: record.user_code || '',
    user_name: record.user_name || '',
    type: record.type || '',
    customer_code: record.customer_code || '',
    customer_name: record.customer_name || '',
    system_name: record.system_name || '',
    distributor_name: record.distributor_name || '',
    visit_date: record.visit_date || '',
    visit_time: record.visit_time || '',
    visit_purpose: record.visit_purpose || '',
    visit_content: record.visit_content || '',
    visit_photos: record.visit_photos || '',
    visit_address: record.visit_address || '',
    province: record.province || '',
    city: record.city || '',
    district: record.district || '',
    actual_start_time: record.actual_start_time || '',
  };
  // 显示模态框
  detailModalVisible.value = true;
}

// 模态框确认按钮处理
function handleDetailModalOk() {
  detailModalVisible.value = false;
}
// 预览模态框取消按钮处理
function handlePreviewModalCancel() {
  previewModalVisible.value = false;
  previewUrl.value = '';
}
// 模态框取消按钮处理
function handleDetailModalCancel() {
  detailModalVisible.value = false;
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    // {
    //   label: '修改保质期',
    //   onClick: editqualityDay.bind(null, record),
    // },
  ];
}
</script>

<style scoped></style>