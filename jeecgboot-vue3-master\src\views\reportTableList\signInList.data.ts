import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { defHttp } from '/@/utils/http/axios';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '用户编码',
    resizable: true,
    align: 'center',
    dataIndex: 'user_code',
    width: 120,
  },
  {
    title: '用户名称',
    resizable: true,
    align: 'center',
    dataIndex: 'user_name',
    width: 120,
  },
  {
    title: '签到/签退时间',
    resizable: true,
    align: 'center',
    dataIndex: 'check_in_time',
    width: 160,
  },
  {
    title: '省',
    resizable: true,
    align: 'center',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '市',
    resizable: true,
    align: 'center',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '区',
    resizable: true,
    align: 'center',
    dataIndex: 'district',
    width: 100,
  },
  {
    title: '地址',
    resizable: true,
    align: 'center',
    dataIndex: 'address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '图片',
    resizable: true,
    align: 'center',
    dataIndex: 'pic',
    width: 120,
    slots: { customRender: 'pic' },
  },
  {
    title: '类型',
    resizable: true,
    align: 'center',
    dataIndex: 'type',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '签到' : '签退';
    },
  },
  {
    title: '状态',
    resizable: true,
    align: 'center',
    dataIndex: 'status',
    width: 80,
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '用户编码',
    field: 'user_code',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '用户名称',
    field: 'user_name',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    label: '类型',
    field: 'type',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '签到', value: '1' },
        { label: '签退', value: '2' },
      ],
    },
  },
  {
    label: '开始日期',
    field: 'check_in_time_begin',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '结束日期',
    field: 'check_in_time_end',
    component: 'DatePicker',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];